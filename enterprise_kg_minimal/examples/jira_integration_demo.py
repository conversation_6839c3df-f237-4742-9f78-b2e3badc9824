#!/usr/bin/env python3
"""
Jira Integration Demo for Enterprise KG

This script demonstrates how to process Jira API data and create knowledge graphs
that can answer queries like "What is UserA's pending task for Project A for today?"

The demo shows:
1. How to format Jira API data for the knowledge graph
2. Processing Jira issues, projects, and user assignments
3. Querying for user tasks and project assignments
4. Using dummy embeddings (no vector DB integration needed)
"""

import json
from datetime import datetime, date
from typing import Dict, Any, List
from enterprise_kg_minimal import process_document
from enterprise_kg_minimal.search import create_hybrid_search_engine, SearchStrategy


def create_sample_jira_data() -> Dict[str, Any]:
    """
    Create sample Jira API data structure that mimics real Jira API responses.
    This represents data you would typically get from Jira REST API.
    """
    return {
        "project": {
            "key": "PROJ-A",
            "name": "Project Alpha",
            "description": "Strategic AI initiative for customer analytics"
        },
        "issues": [
            {
                "key": "PROJ-A-123",
                "summary": "Implement user authentication system",
                "description": "Create secure login system with multi-factor authentication",
                "issue_type": "Task",
                "status": "In Progress",
                "priority": "High",
                "assignee": "UserA",
                "reporter": "ProjectManager",
                "created": "2024-01-15T09:00:00Z",
                "due_date": "2024-01-20T17:00:00Z",
                "sprint": "Sprint 2024-01",
                "story_points": 8,
                "labels": ["security", "authentication", "backend"],
                "comments": [
                    {
                        "author": "UserA",
                        "created": "2024-01-16T10:30:00Z",
                        "body": "Started working on OAuth integration"
                    }
                ]
            },
            {
                "key": "PROJ-A-124",
                "summary": "Design user dashboard mockups",
                "description": "Create wireframes and mockups for the main user dashboard",
                "issue_type": "Story",
                "status": "To Do",
                "priority": "Medium",
                "assignee": "UserB",
                "reporter": "ProductOwner",
                "created": "2024-01-16T14:00:00Z",
                "due_date": "2024-01-22T17:00:00Z",
                "sprint": "Sprint 2024-01",
                "story_points": 5,
                "labels": ["design", "ui", "frontend"]
            },
            {
                "key": "PROJ-A-125",
                "summary": "Database performance optimization",
                "description": "Optimize database queries for better performance",
                "issue_type": "Task",
                "status": "Done",
                "priority": "High",
                "assignee": "UserA",
                "reporter": "TechLead",
                "created": "2024-01-10T09:00:00Z",
                "due_date": "2024-01-18T17:00:00Z",
                "resolved": "2024-01-17T16:30:00Z",
                "sprint": "Sprint 2024-01",
                "story_points": 13,
                "labels": ["performance", "database", "backend"]
            }
        ],
        "users": [
            {
                "username": "UserA",
                "display_name": "Alice Johnson",
                "email": "<EMAIL>",
                "role": "Senior Developer"
            },
            {
                "username": "UserB", 
                "display_name": "Bob Smith",
                "email": "<EMAIL>",
                "role": "UI Designer"
            }
        ]
    }


def format_jira_data_for_kg(jira_data: Dict[str, Any]) -> str:
    """
    Convert Jira API data into a text format suitable for knowledge graph processing.
    This simulates how you would transform Jira API responses into document content.
    """
    content_parts = []
    
    # Project information
    project = jira_data["project"]
    content_parts.append(f"Project: {project['name']} ({project['key']})")
    content_parts.append(f"Description: {project['description']}")
    content_parts.append("")
    
    # User information
    content_parts.append("Team Members:")
    for user in jira_data["users"]:
        content_parts.append(f"- {user['display_name']} ({user['username']}) - {user['role']}")
    content_parts.append("")
    
    # Issues information
    content_parts.append("Issues and Tasks:")
    for issue in jira_data["issues"]:
        content_parts.append(f"Issue {issue['key']}: {issue['summary']}")
        content_parts.append(f"  Type: {issue['issue_type']}")
        content_parts.append(f"  Status: {issue['status']}")
        content_parts.append(f"  Priority: {issue['priority']}")
        content_parts.append(f"  Assigned to: {issue['assignee']}")
        content_parts.append(f"  Reporter: {issue['reporter']}")
        content_parts.append(f"  Due Date: {issue['due_date']}")
        content_parts.append(f"  Sprint: {issue['sprint']}")
        content_parts.append(f"  Story Points: {issue['story_points']}")
        content_parts.append(f"  Description: {issue['description']}")
        
        if issue.get('resolved'):
            content_parts.append(f"  Resolved: {issue['resolved']}")
            
        if issue.get('comments'):
            content_parts.append("  Comments:")
            for comment in issue['comments']:
                content_parts.append(f"    - {comment['author']}: {comment['body']}")
        
        content_parts.append("")
    
    return "\n".join(content_parts)


def process_jira_knowledge_graph(jira_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process Jira data and create a knowledge graph.
    """
    print("🔄 Converting Jira data to knowledge graph format...")
    
    # Convert Jira data to text format
    document_content = format_jira_data_for_kg(jira_data)
    
    print("📄 Sample document content:")
    print("=" * 50)
    print(document_content[:500] + "..." if len(document_content) > 500 else document_content)
    print("=" * 50)
    
    # Process the document to create knowledge graph
    print("\n🔄 Processing document with knowledge graph...")
    result = process_document(
        file_id="jira_project_alpha_data",
        file_content=document_content,
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j", 
        neo4j_password="password",
        content_type="project",  # Use project content type for better entity extraction
        chunking_strategy="hybrid",
        chunk_size=800,
        chunk_overlap=100
    )
    
    return result


def query_user_tasks(project_key: str, username: str, target_date: str = None) -> Dict[str, Any]:
    """
    Query for a user's pending tasks in a specific project.
    This demonstrates how to answer: "What is UserA's pending task for Project A for today?"
    """
    if target_date is None:
        target_date = date.today().strftime("%Y-%m-%d")
    
    print(f"\n🔍 Querying tasks for {username} in {project_key} for {target_date}...")
    
    # Create search engine
    search_engine = create_hybrid_search_engine(
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password"
    )
    
    # For this demo, we'll use dummy chunk indices since we don't have vector DB
    # In real implementation, you'd get these from Pinecone/vector search
    dummy_chunk_indices = ["jira_project_alpha_data_chunk_0", "jira_project_alpha_data_chunk_1"]
    
    try:
        # Search with entity and relationship filters for task management
        result = search_engine.search(
            chunk_indices=dummy_chunk_indices,
            query_text=f"What tasks are assigned to {username} in {project_key} that are pending or in progress?",
            strategy=SearchStrategy.ENTITY_CENTRIC,
            entity_types={"Task", "Issue", "Person", "Project", "Status"},
            relationship_types={"assigned_to", "belongs_to_project", "has_status", "due_on"},
            max_results=20,
            expansion_depth=2
        )
        
        return result
        
    finally:
        search_engine.neo4j_client.close()


def main():
    """
    Main demo function showing complete Jira integration workflow.
    """
    print("🚀 Jira Integration Demo for Enterprise KG")
    print("=" * 60)
    
    # Step 1: Create sample Jira data (simulating API response)
    print("\n1️⃣ Creating sample Jira data...")
    jira_data = create_sample_jira_data()
    print(f"   ✅ Created data for project: {jira_data['project']['name']}")
    print(f"   ✅ {len(jira_data['issues'])} issues")
    print(f"   ✅ {len(jira_data['users'])} users")
    
    # Step 2: Process data into knowledge graph
    print("\n2️⃣ Processing Jira data into knowledge graph...")
    try:
        kg_result = process_jira_knowledge_graph(jira_data)
        
        if kg_result.get('success'):
            print(f"   ✅ Knowledge graph created successfully!")
            print(f"   📊 Chunks created: {kg_result.get('chunks_created', 0)}")
            print(f"   🏷️  Total entities: {kg_result.get('total_entities', 0)}")
            print(f"   🔗 Total relationships: {kg_result.get('total_relationships', 0)}")
        else:
            print(f"   ❌ Failed to create knowledge graph: {kg_result.get('error')}")
            return
            
    except Exception as e:
        print(f"   ❌ Error processing knowledge graph: {e}")
        return
    
    # Step 3: Query for user tasks
    print("\n3️⃣ Querying user tasks...")
    try:
        query_result = query_user_tasks("PROJ-A", "UserA")
        
        print(f"   ✅ Query executed successfully!")
        print(f"   📊 Total results: {query_result.total_results}")
        print(f"   ⏱️  Processing time: {query_result.processing_time_ms:.2f}ms")
        print(f"   🎯 Relevance score: {query_result.relevance_score:.2f}")
        
        # Display found entities and relationships
        if query_result.graph_context.entities:
            print(f"\n   🏷️  Found entities ({len(query_result.graph_context.entities)}):")
            for entity in query_result.graph_context.entities[:5]:  # Show first 5
                print(f"      - {entity.name} ({entity.entity_type})")
        
        if query_result.graph_context.relationships:
            print(f"\n   🔗 Found relationships ({len(query_result.graph_context.relationships)}):")
            for rel in query_result.graph_context.relationships[:5]:  # Show first 5
                print(f"      - {rel.subject} --{rel.relationship_type}--> {rel.object}")
                
    except Exception as e:
        print(f"   ❌ Error querying tasks: {e}")
    
    print("\n✨ Demo completed!")
    print("\n💡 Key Insights:")
    print("   • Jira API data can be easily converted to knowledge graph format")
    print("   • The system can extract entities like Issues, Tasks, Users, Projects")
    print("   • Relationships like 'assigned_to', 'has_status', 'due_on' are captured")
    print("   • Complex queries about user tasks and project assignments are supported")
    print("   • No vector database needed for basic functionality (dummy embeddings work)")


if __name__ == "__main__":
    main()
