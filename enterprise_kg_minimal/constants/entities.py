"""
Entity type definitions for Enterprise KG

This module defines all supported entity types in the enterprise knowledge graph.
Add new entity types here as the system evolves.
"""

from enum import Enum
from typing import Set, Dict, Any


class EntityType(Enum):
    """
    Enumeration of all supported entity types in the enterprise knowledge graph.

    Each entity type represents a distinct category of entities that can be
    extracted from enterprise documents.
    """

    # People and Roles
    PERSON = "Person"
    EMPLOYEE = "Employee"
    MANAGER = "Manager"
    EXECUTIVE = "Executive"
    CONSULTANT = "Consultant"
    CLIENT = "Client"
    STAKEHOLDER = "Stakeholder"

    # Projects and Initiatives
    PROJECT = "Project"
    INITIATIVE = "Initiative"
    PROGRAM = "Program"
    CAMPAIGN = "Campaign"

    # Organizations
    COMPANY = "Company"
    DEPARTMENT = "Department"
    TEAM = "Team"
    VENDOR = "Vendor"
    PARTNER = "Partner"
    COMPETITOR = "Competitor"

    # Documents and Assets
    DOCUMENT = "Document"
    REPORT = "Report"
    PROPOSAL = "Proposal"
    CONTRACT = "Contract"
    POLICY = "Policy"
    PROCEDURE = "Procedure"

    # Technology and Systems
    SYSTEM = "System"
    APPLICATION = "Application"
    DATABASE = "Database"
    PLATFORM = "Platform"
    TOOL = "Tool"
    TECHNOLOGY = "Technology"

    # Business Concepts
    PROCESS = "Process"
    WORKFLOW = "Workflow"
    REQUIREMENT = "Requirement"
    OBJECTIVE = "Objective"
    GOAL = "Goal"
    METRIC = "Metric"
    KPI = "KPI"

    # Financial
    BUDGET = "Budget"
    COST = "Cost"
    REVENUE = "Revenue"
    INVESTMENT = "Investment"

    # Locations
    OFFICE = "Office"
    LOCATION = "Location"
    REGION = "Region"
    COUNTRY = "Country"

    # Time-based
    MILESTONE = "Milestone"
    DEADLINE = "Deadline"
    PHASE = "Phase"
    QUARTER = "Quarter"
    YEAR = "Year"

    # Customer Feedback and Sentiment
    FEEDBACK = "Feedback"
    CUSTOMER = "Customer"
    PRODUCT = "Product"
    SERVICE = "Service"
    SENTIMENT = "Sentiment"
    REVIEW = "Review"
    RATING = "Rating"
    COMPLAINT = "Complaint"
    COMPLIMENT = "Compliment"

    # Jira and Task Management
    ISSUE = "Issue"
    TASK = "Task"
    TICKET = "Ticket"
    EPIC = "Epic"
    STORY = "Story"
    SUBTASK = "Subtask"
    BUG = "Bug"
    SPRINT = "Sprint"
    STATUS = "Status"
    PRIORITY = "Priority"
    RESOLUTION = "Resolution"
    COMMENT = "Comment"
    ATTACHMENT = "Attachment"
    WORKLOG = "Worklog"

    # Generic
    ENTITY = "Entity"  # Fallback for unclassified entities


# Helper functions for entity type management
def get_all_entity_types() -> Set[str]:
    """Get all entity type values as strings."""
    return {entity_type.value for entity_type in EntityType}


def get_person_related_types() -> Set[str]:
    """Get entity types related to people and roles."""
    return {
        EntityType.PERSON.value,
        EntityType.EMPLOYEE.value,
        EntityType.MANAGER.value,
        EntityType.EXECUTIVE.value,
        EntityType.CONSULTANT.value,
        EntityType.CLIENT.value,
        EntityType.STAKEHOLDER.value,
    }


def get_project_related_types() -> Set[str]:
    """Get entity types related to projects and initiatives."""
    return {
        EntityType.PROJECT.value,
        EntityType.INITIATIVE.value,
        EntityType.PROGRAM.value,
        EntityType.CAMPAIGN.value,
    }


def get_organization_related_types() -> Set[str]:
    """Get entity types related to organizations."""
    return {
        EntityType.COMPANY.value,
        EntityType.DEPARTMENT.value,
        EntityType.TEAM.value,
        EntityType.VENDOR.value,
        EntityType.PARTNER.value,
        EntityType.COMPETITOR.value,
    }


def get_feedback_related_types() -> Set[str]:
    """Get entity types related to customer feedback and sentiment."""
    return {
        EntityType.FEEDBACK.value,
        EntityType.CUSTOMER.value,
        EntityType.PRODUCT.value,
        EntityType.SERVICE.value,
        EntityType.SENTIMENT.value,
        EntityType.REVIEW.value,
        EntityType.RATING.value,
        EntityType.COMPLAINT.value,
        EntityType.COMPLIMENT.value,
    }


def get_jira_related_types() -> Set[str]:
    """Get entity types related to Jira and task management."""
    return {
        EntityType.ISSUE.value,
        EntityType.TASK.value,
        EntityType.TICKET.value,
        EntityType.EPIC.value,
        EntityType.STORY.value,
        EntityType.SUBTASK.value,
        EntityType.BUG.value,
        EntityType.SPRINT.value,
        EntityType.STATUS.value,
        EntityType.PRIORITY.value,
        EntityType.RESOLUTION.value,
        EntityType.COMMENT.value,
        EntityType.ATTACHMENT.value,
        EntityType.WORKLOG.value,
    }


def get_system_related_types() -> Set[str]:
    """Get entity types related to systems and technology."""
    return {
        EntityType.SYSTEM.value,
        EntityType.APPLICATION.value,
        EntityType.DATABASE.value,
        EntityType.PLATFORM.value,
        EntityType.TOOL.value,
        EntityType.TECHNOLOGY.value,
    }


def get_document_related_types() -> Set[str]:
    """Get entity types related to documents."""
    return {
        EntityType.DOCUMENT.value,
        EntityType.REPORT.value,
        EntityType.PROPOSAL.value,
        EntityType.CONTRACT.value,
        EntityType.POLICY.value,
        EntityType.PROCEDURE.value,
    }


def get_organizational_hierarchy_types() -> Set[str]:
    """
    Get entity types that represent organizational hierarchy and structure.

    This includes both people in organizational roles and organizational units
    that are commonly used in hierarchical search strategies.

    Returns:
        Set of entity type strings representing organizational hierarchy
    """
    return {
        EntityType.PERSON.value,
        EntityType.EMPLOYEE.value,
        EntityType.MANAGER.value,
        EntityType.EXECUTIVE.value,
        EntityType.COMPANY.value,
        EntityType.DEPARTMENT.value,
        EntityType.TEAM.value,
    }


def is_valid_entity_type(entity_type: str) -> bool:
    """Check if a string is a valid entity type."""
    return entity_type in get_all_entity_types()


def get_entity_type_description(entity_type: EntityType) -> str:
    """Get a human-readable description for an entity type."""
    descriptions = {
        EntityType.PERSON: "An individual person mentioned in the document",
        EntityType.EMPLOYEE: "A company employee",
        EntityType.MANAGER: "A person in a management role",
        EntityType.EXECUTIVE: "A senior executive or C-level person",
        EntityType.CONSULTANT: "An external consultant or advisor",
        EntityType.CLIENT: "A customer or client of the organization",
        EntityType.STAKEHOLDER: "A person with interest in the project/organization",

        EntityType.PROJECT: "A specific project or work initiative",
        EntityType.INITIATIVE: "A strategic initiative or program",
        EntityType.PROGRAM: "A large-scale program containing multiple projects",
        EntityType.CAMPAIGN: "A marketing or business campaign",

        EntityType.COMPANY: "A company or corporation",
        EntityType.DEPARTMENT: "An organizational department",
        EntityType.TEAM: "A working team or group",
        EntityType.VENDOR: "An external vendor or supplier",
        EntityType.PARTNER: "A business partner",
        EntityType.COMPETITOR: "A competitor organization",

        EntityType.DOCUMENT: "A document or file",
        EntityType.REPORT: "A formal report",
        EntityType.PROPOSAL: "A business proposal",
        EntityType.CONTRACT: "A legal contract or agreement",
        EntityType.POLICY: "An organizational policy",
        EntityType.PROCEDURE: "A standard operating procedure",

        EntityType.SYSTEM: "A computer or business system",
        EntityType.APPLICATION: "A software application",
        EntityType.DATABASE: "A database system",
        EntityType.PLATFORM: "A technology platform",
        EntityType.TOOL: "A software tool or utility",
        EntityType.TECHNOLOGY: "A technology or technical solution",

        EntityType.PROCESS: "A business process",
        EntityType.WORKFLOW: "A defined workflow",
        EntityType.REQUIREMENT: "A business or technical requirement",
        EntityType.OBJECTIVE: "A business objective",
        EntityType.GOAL: "A specific goal or target",
        EntityType.METRIC: "A measurement or metric",
        EntityType.KPI: "A key performance indicator",

        EntityType.BUDGET: "A financial budget",
        EntityType.COST: "A cost or expense",
        EntityType.REVENUE: "Revenue or income",
        EntityType.INVESTMENT: "An investment or funding",

        EntityType.OFFICE: "A physical office location",
        EntityType.LOCATION: "A geographic location",
        EntityType.REGION: "A geographic region",
        EntityType.COUNTRY: "A country",

        EntityType.MILESTONE: "A project milestone",
        EntityType.DEADLINE: "A deadline or due date",
        EntityType.PHASE: "A project phase",
        EntityType.QUARTER: "A business quarter",
        EntityType.YEAR: "A specific year",

        # Customer Feedback and Sentiment
        EntityType.FEEDBACK: "Customer feedback or opinion",
        EntityType.CUSTOMER: "A customer or client providing feedback",
        EntityType.PRODUCT: "A product or service being reviewed",
        EntityType.SERVICE: "A service being evaluated",
        EntityType.SENTIMENT: "Emotional sentiment or opinion",
        EntityType.REVIEW: "A formal review or evaluation",
        EntityType.RATING: "A numerical or categorical rating",
        EntityType.COMPLAINT: "A customer complaint or issue",
        EntityType.COMPLIMENT: "Positive feedback or praise",

        # Jira and Task Management
        EntityType.ISSUE: "A Jira issue or work item",
        EntityType.TASK: "A specific task or work assignment",
        EntityType.TICKET: "A support or work ticket",
        EntityType.EPIC: "A large work initiative containing multiple stories",
        EntityType.STORY: "A user story or feature requirement",
        EntityType.SUBTASK: "A subtask of a larger issue",
        EntityType.BUG: "A software bug or defect",
        EntityType.SPRINT: "A time-boxed development iteration",
        EntityType.STATUS: "The current status of an issue or task",
        EntityType.PRIORITY: "The priority level of an issue",
        EntityType.RESOLUTION: "The resolution of a completed issue",
        EntityType.COMMENT: "A comment on an issue or task",
        EntityType.ATTACHMENT: "A file attachment to an issue",
        EntityType.WORKLOG: "A time log entry for work performed",

        EntityType.ENTITY: "A generic entity",
    }

    return descriptions.get(entity_type, "Unknown entity type")


def get_entity_category_mapping() -> Dict[str, callable]:
    """
    Get mapping of category names to their corresponding getter functions.
    This centralizes all entity categorization logic in one place.

    To add a new category:
    1. Create a new get_[category]_related_types() function above
    2. Add it to this mapping

    Returns:
        Dictionary mapping category names to getter functions
    """
    return {
        "People & Roles": get_person_related_types,
        "Organizations": get_organization_related_types,
        "Organizational Hierarchy": get_organizational_hierarchy_types,
        "Projects & Initiatives": get_project_related_types,
        "Systems & Technology": get_system_related_types,
        "Documents": get_document_related_types,
        "Customer Feedback": get_feedback_related_types,
        "Jira & Task Management": get_jira_related_types,
        # Add new categories here as needed
        # "Financial Objects": get_financial_related_types,
        # "Location Objects": get_location_related_types,
    }


def get_entity_properties(entity_type: str) -> Dict[str, Any]:
    """
    Get additional properties for an entity type that enhance GraphRAG context.

    These properties provide rich context for knowledge graph queries and
    improve the effectiveness of GraphRAG by adding semantic information.

    Args:
        entity_type: The entity type (e.g., "Person", "Company", "Project")

    Returns:
        Dictionary of properties to add to the node
    """
    # Convert string to EntityType enum if needed
    if isinstance(entity_type, str):
        try:
            entity_enum = EntityType(entity_type)
        except ValueError:
            entity_enum = EntityType.ENTITY
    else:
        entity_enum = entity_type

    # Base properties for all entities
    base_properties = {
        "description": get_entity_type_description(entity_enum),
        "category": _get_entity_category(entity_enum),
        "searchable": True,
        "graph_importance": _get_graph_importance(entity_enum)
    }

    # Type-specific properties for enhanced GraphRAG context
    type_specific_properties = {
        # People and Roles
        EntityType.PERSON: {
            "is_human": True,
            "can_have_relationships": True,
            "typical_relationships": ["works_for", "manages", "reports_to", "involved_in"],
            "context_keywords": ["employee", "person", "individual", "staff", "team member"]
        },
        EntityType.EMPLOYEE: {
            "is_human": True,
            "employment_status": "active",
            "can_have_relationships": True,
            "typical_relationships": ["works_for", "reports_to", "member_of", "involved_in"],
            "context_keywords": ["worker", "staff", "team member", "personnel"]
        },
        EntityType.MANAGER: {
            "is_human": True,
            "leadership_role": True,
            "can_have_relationships": True,
            "typical_relationships": ["manages", "leads", "works_for", "reports_to"],
            "context_keywords": ["supervisor", "leader", "head", "director", "boss"]
        },
        EntityType.EXECUTIVE: {
            "is_human": True,
            "leadership_role": True,
            "seniority_level": "executive",
            "can_have_relationships": True,
            "typical_relationships": ["leads", "manages", "owns", "responsible_for"],
            "context_keywords": ["CEO", "CTO", "VP", "president", "chief", "senior"]
        },

        # Organizations
        EntityType.COMPANY: {
            "is_organization": True,
            "can_employ_people": True,
            "can_have_relationships": True,
            "typical_relationships": ["employs", "partners_with", "contracts_with", "owns"],
            "context_keywords": ["corporation", "business", "firm", "enterprise", "organization"]
        },
        EntityType.DEPARTMENT: {
            "is_organization": True,
            "is_internal_unit": True,
            "can_have_relationships": True,
            "typical_relationships": ["part_of", "contains", "manages", "responsible_for"],
            "context_keywords": ["division", "unit", "group", "section", "branch"]
        },
        EntityType.TEAM: {
            "is_organization": True,
            "is_internal_unit": True,
            "collaborative_unit": True,
            "can_have_relationships": True,
            "typical_relationships": ["member_of", "works_on", "reports_to", "collaborates_with"],
            "context_keywords": ["group", "squad", "crew", "unit", "workforce"]
        },

        # Projects and Initiatives
        EntityType.PROJECT: {
            "is_initiative": True,
            "has_timeline": True,
            "can_have_relationships": True,
            "typical_relationships": ["involves", "managed_by", "funded_by", "depends_on"],
            "context_keywords": ["initiative", "effort", "undertaking", "program", "work"]
        },
        EntityType.INITIATIVE: {
            "is_initiative": True,
            "strategic_importance": "high",
            "can_have_relationships": True,
            "typical_relationships": ["involves", "led_by", "supports", "enables"],
            "context_keywords": ["program", "effort", "campaign", "strategy", "plan"]
        },

        # Technology and Systems
        EntityType.SYSTEM: {
            "is_technology": True,
            "can_be_integrated": True,
            "can_have_relationships": True,
            "typical_relationships": ["integrates_with", "runs_on", "accesses", "hosts"],
            "context_keywords": ["platform", "software", "application", "infrastructure"]
        },
        EntityType.APPLICATION: {
            "is_technology": True,
            "is_software": True,
            "can_have_relationships": True,
            "typical_relationships": ["runs_on", "integrates_with", "accesses", "used_by"],
            "context_keywords": ["software", "app", "program", "tool", "system"]
        },
        EntityType.DATABASE: {
            "is_technology": True,
            "stores_data": True,
            "can_have_relationships": True,
            "typical_relationships": ["contains", "accessed_by", "runs_on", "backs_up_to"],
            "context_keywords": ["data store", "repository", "storage", "DB"]
        },

        # Documents
        EntityType.DOCUMENT: {
            "is_information": True,
            "contains_knowledge": True,
            "can_have_relationships": True,
            "typical_relationships": ["authored_by", "references", "mentions", "supersedes"],
            "context_keywords": ["file", "record", "paper", "report", "documentation"]
        },
        EntityType.REPORT: {
            "is_information": True,
            "contains_knowledge": True,
            "formal_document": True,
            "can_have_relationships": True,
            "typical_relationships": ["authored_by", "covers", "mentions", "reviewed_by"],
            "context_keywords": ["analysis", "summary", "findings", "documentation"]
        },

        # Business Concepts
        EntityType.PROCESS: {
            "is_business_concept": True,
            "has_steps": True,
            "can_have_relationships": True,
            "typical_relationships": ["involves", "managed_by", "follows", "enables"],
            "context_keywords": ["procedure", "workflow", "method", "approach"]
        },
        EntityType.GOAL: {
            "is_business_concept": True,
            "has_target": True,
            "measurable": True,
            "can_have_relationships": True,
            "typical_relationships": ["owned_by", "supports", "measured_by", "achieved_by"],
            "context_keywords": ["objective", "target", "aim", "purpose", "outcome"]
        },

        # Locations
        EntityType.OFFICE: {
            "is_location": True,
            "physical_space": True,
            "can_have_relationships": True,
            "typical_relationships": ["located_in", "houses", "belongs_to", "contains"],
            "context_keywords": ["building", "workplace", "facility", "location", "site"]
        },
        EntityType.LOCATION: {
            "is_location": True,
            "geographic": True,
            "can_have_relationships": True,
            "typical_relationships": ["contains", "located_in", "part_of", "borders"],
            "context_keywords": ["place", "area", "region", "site", "position"]
        },

        # Jira and Task Management
        EntityType.ISSUE: {
            "is_work_item": True,
            "has_status": True,
            "has_assignee": True,
            "trackable": True,
            "can_have_relationships": True,
            "typical_relationships": ["assigned_to", "has_status", "belongs_to_project", "created_by", "due_on"],
            "context_keywords": ["jira", "issue", "work", "task", "ticket", "item"]
        },
        EntityType.TASK: {
            "is_work_item": True,
            "has_status": True,
            "has_assignee": True,
            "actionable": True,
            "can_have_relationships": True,
            "typical_relationships": ["assigned_to", "has_status", "part_of", "depends_on", "due_on"],
            "context_keywords": ["task", "work", "assignment", "todo", "action"]
        },
        EntityType.EPIC: {
            "is_work_item": True,
            "strategic_importance": "high",
            "contains_stories": True,
            "can_have_relationships": True,
            "typical_relationships": ["contains", "owned_by", "involves", "enables"],
            "context_keywords": ["epic", "initiative", "large", "strategic", "theme"]
        },
        EntityType.SPRINT: {
            "is_time_boxed": True,
            "has_duration": True,
            "contains_work": True,
            "can_have_relationships": True,
            "typical_relationships": ["contains", "planned_for", "involves", "managed_by"],
            "context_keywords": ["sprint", "iteration", "cycle", "period", "timeframe"]
        },
        EntityType.STATUS: {
            "is_state": True,
            "indicates_progress": True,
            "can_have_relationships": True,
            "typical_relationships": ["status_of", "indicates", "transitions_to"],
            "context_keywords": ["status", "state", "progress", "condition", "stage"]
        }
    }

    # Get type-specific properties
    specific_props = type_specific_properties.get(entity_enum, {})

    # Merge base and specific properties
    all_properties = {**base_properties, **specific_props}

    return all_properties


def _get_entity_category(entity_type: EntityType) -> str:
    """Get the high-level category for an entity type."""
    categories = {
        # People
        EntityType.PERSON: "People",
        EntityType.EMPLOYEE: "People",
        EntityType.MANAGER: "People",
        EntityType.EXECUTIVE: "People",
        EntityType.CONSULTANT: "People",
        EntityType.CLIENT: "People",
        EntityType.STAKEHOLDER: "People",

        # Organizations
        EntityType.COMPANY: "Organizations",
        EntityType.DEPARTMENT: "Organizations",
        EntityType.TEAM: "Organizations",
        EntityType.VENDOR: "Organizations",
        EntityType.PARTNER: "Organizations",
        EntityType.COMPETITOR: "Organizations",

        # Projects
        EntityType.PROJECT: "Projects",
        EntityType.INITIATIVE: "Projects",
        EntityType.PROGRAM: "Projects",
        EntityType.CAMPAIGN: "Projects",

        # Technology
        EntityType.SYSTEM: "Technology",
        EntityType.APPLICATION: "Technology",
        EntityType.DATABASE: "Technology",
        EntityType.PLATFORM: "Technology",
        EntityType.TOOL: "Technology",
        EntityType.TECHNOLOGY: "Technology",

        # Documents
        EntityType.DOCUMENT: "Documents",
        EntityType.REPORT: "Documents",
        EntityType.PROPOSAL: "Documents",
        EntityType.CONTRACT: "Documents",
        EntityType.POLICY: "Documents",
        EntityType.PROCEDURE: "Documents",

        # Business Concepts
        EntityType.PROCESS: "Business Concepts",
        EntityType.WORKFLOW: "Business Concepts",
        EntityType.REQUIREMENT: "Business Concepts",
        EntityType.OBJECTIVE: "Business Concepts",
        EntityType.GOAL: "Business Concepts",
        EntityType.METRIC: "Business Concepts",
        EntityType.KPI: "Business Concepts",

        # Financial
        EntityType.BUDGET: "Financial",
        EntityType.COST: "Financial",
        EntityType.REVENUE: "Financial",
        EntityType.INVESTMENT: "Financial",

        # Locations
        EntityType.OFFICE: "Locations",
        EntityType.LOCATION: "Locations",
        EntityType.REGION: "Locations",
        EntityType.COUNTRY: "Locations",

        # Time-based
        EntityType.MILESTONE: "Time-based",
        EntityType.DEADLINE: "Time-based",
        EntityType.PHASE: "Time-based",
        EntityType.QUARTER: "Time-based",
        EntityType.YEAR: "Time-based",

        # Customer Feedback
        EntityType.FEEDBACK: "Customer Feedback",
        EntityType.CUSTOMER: "Customer Feedback",
        EntityType.PRODUCT: "Customer Feedback",
        EntityType.SERVICE: "Customer Feedback",
        EntityType.SENTIMENT: "Customer Feedback",
        EntityType.REVIEW: "Customer Feedback",
        EntityType.RATING: "Customer Feedback",
        EntityType.COMPLAINT: "Customer Feedback",
        EntityType.COMPLIMENT: "Customer Feedback",

        # Jira and Task Management
        EntityType.ISSUE: "Jira & Tasks",
        EntityType.TASK: "Jira & Tasks",
        EntityType.TICKET: "Jira & Tasks",
        EntityType.EPIC: "Jira & Tasks",
        EntityType.STORY: "Jira & Tasks",
        EntityType.SUBTASK: "Jira & Tasks",
        EntityType.BUG: "Jira & Tasks",
        EntityType.SPRINT: "Jira & Tasks",
        EntityType.STATUS: "Jira & Tasks",
        EntityType.PRIORITY: "Jira & Tasks",
        EntityType.RESOLUTION: "Jira & Tasks",
        EntityType.COMMENT: "Jira & Tasks",
        EntityType.ATTACHMENT: "Jira & Tasks",
        EntityType.WORKLOG: "Jira & Tasks",
    }

    return categories.get(entity_type, "General")


def _get_graph_importance(entity_type: EntityType) -> float:
    """
    Get the graph importance score for an entity type.

    Higher scores indicate entities that are typically more central
    to business operations and GraphRAG queries.
    """
    importance_scores = {
        # High importance - central to business operations
        EntityType.PERSON: 0.9,
        EntityType.EMPLOYEE: 0.9,
        EntityType.MANAGER: 0.95,
        EntityType.EXECUTIVE: 1.0,
        EntityType.COMPANY: 0.95,
        EntityType.PROJECT: 0.9,
        EntityType.INITIATIVE: 0.9,

        # Medium-high importance
        EntityType.DEPARTMENT: 0.8,
        EntityType.TEAM: 0.8,
        EntityType.SYSTEM: 0.8,
        EntityType.APPLICATION: 0.75,
        EntityType.PROCESS: 0.8,
        EntityType.GOAL: 0.8,

        # Medium importance
        EntityType.CONSULTANT: 0.7,
        EntityType.CLIENT: 0.7,
        EntityType.VENDOR: 0.7,
        EntityType.PARTNER: 0.7,
        EntityType.DOCUMENT: 0.7,
        EntityType.REPORT: 0.7,
        EntityType.DATABASE: 0.75,

        # Lower importance but still relevant
        EntityType.TOOL: 0.6,
        EntityType.PROCEDURE: 0.6,
        EntityType.METRIC: 0.6,
        EntityType.OFFICE: 0.6,
        EntityType.LOCATION: 0.6,

        # Contextual importance
        EntityType.MILESTONE: 0.5,
        EntityType.DEADLINE: 0.5,
        EntityType.PHASE: 0.5,
        EntityType.QUARTER: 0.4,
        EntityType.YEAR: 0.3,

        # Customer Feedback importance
        EntityType.FEEDBACK: 0.85,
        EntityType.CUSTOMER: 0.8,
        EntityType.PRODUCT: 0.75,
        EntityType.SERVICE: 0.75,
        EntityType.SENTIMENT: 0.7,
        EntityType.REVIEW: 0.8,
        EntityType.RATING: 0.7,
        EntityType.COMPLAINT: 0.9,  # High importance for issues
        EntityType.COMPLIMENT: 0.7,

        # Jira and Task Management importance
        EntityType.ISSUE: 0.9,  # High importance for work tracking
        EntityType.TASK: 0.85,
        EntityType.TICKET: 0.8,
        EntityType.EPIC: 0.95,  # Very high importance for strategic work
        EntityType.STORY: 0.8,
        EntityType.SUBTASK: 0.7,
        EntityType.BUG: 0.9,  # High importance for defects
        EntityType.SPRINT: 0.85,  # High importance for planning
        EntityType.STATUS: 0.8,  # Important for tracking progress
        EntityType.PRIORITY: 0.75,
        EntityType.RESOLUTION: 0.7,
        EntityType.COMMENT: 0.5,
        EntityType.ATTACHMENT: 0.4,
        EntityType.WORKLOG: 0.6,
    }

    return importance_scores.get(entity_type, 0.5)
